// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#include <iostream>
#include <algorithm>
#include <memory>

#include "base/include/module.h"
#include "path/src/base/uuid.h"
#include "debug/geojson_writter.h"
#include "debug/geojson_reader.h"
#include "path_module.h"
#include "pointll.h"
#include "logger.h"
#include "debug/route_evaluator.h"
#include <boost/filesystem.hpp>
using namespace aurora::path;
using namespace aurora;

namespace fs = boost::filesystem;
class PathListenerDemo : public PathListener {
private:
    RouteEvaluator evaluator_;

public:
    void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override {
        std::cout << "Path result received" << "path_result->size(): " << result->paths.size() << std::endl;
        // print path result
        std::cout << "Path result uuid: " << result->uuid << std::endl;
        std::cout << "Path result status: " << result->status << std::endl;
        std::cout << "Path result code: " << result->code << std::endl;
        std::cout << "Path result tag: " << result->tag << std::endl;
        std::cout << "Path result query_time_ms: " << result->metadata.query_time_ms << std::endl;
        std::cout << "Path result paths: " << result->paths.size() << std::endl;

        // 使用路线评测器进行评测
        if (!result->paths.empty()) {
            std::cout << "\n======== 路线评测开始 ========" << std::endl;

            // 评测所有路线
            std::vector<RouteEvaluationResult> evaluation_results;
            for (size_t i = 0; i < result->paths.size(); ++i) {
                auto eval_result = evaluator_.EvaluateRoute(query, result, i);
                evaluation_results.push_back(eval_result);
                evaluator_.PrintEvaluationResult(eval_result);
                std::cout << std::endl;
            }

            // 生成评测报告和可视化
            if (evaluation_results.size() > 1) {
                evaluator_.GenerateReport(evaluation_results);
                evaluator_.GenerateVisualization(evaluation_results);
            }

            // 如果有多条路线，进行对比分析
            if (result->paths.size() >= 2) {
                std::cout << "\n======== 路线对比分析 ========" << std::endl;

                // 比较所有可能的路线组合
                for (size_t i = 0; i < result->paths.size(); ++i) {
                    for (size_t j = i + 1; j < result->paths.size(); ++j) {
                        std::cout << "\n--- 路线 " << (i + 1) << " vs 路线 " << (j + 1) << " ---" << std::endl;
                        auto comparison = evaluator_.CompareRoutes(query, result, i, j);
                        evaluator_.PrintComparisonResult(comparison);
                        evaluator_.GenerateComparisonReport(comparison);
                        evaluator_.GenerateComparisonVisualization(comparison);
                        std::cout << std::endl;
                    }
                }
            }

            std::cout << "======== 路线评测结束 ========\n" << std::endl;
        }
        for (const auto& path : result->paths) {
            std::cout << "Path result path: " << path.path_id << std::endl;
            std::cout << "Path result path length: " << path.length << std::endl;
            std::cout << "Path result path travel time: " << path.travel_time << std::endl;
            std::cout << "Path result path traffic_light_num: " << path.traffic_light_num << std::endl;
            std::cout << "Path result path sections: " << path.sections.size() << std::endl;
            for (const auto& section : path.sections) {
                std::cout << "Path result path section start index: " << section.index << std::endl;
                std::cout << "Path result path section num: " << section.num << std::endl;
                std::cout << "Path result path section length: " << section.length << std::endl;
                std::cout << "Path result path section time: " << section.time << std::endl;
                std::cout << "Path result path section start_offset: " << section.start_offset << std::endl;
                std::cout << "Path result path section end_offset: " << section.end_offset << std::endl;
            }

            // start print path links
            std::cout << "Path result path links: " << path.links.size() << std::endl;
            // for (const auto& link : path.links) {
            //     // print in one line
            //     std::cout << "Path result path link tile_id: " << link->tile_id << " id: " << link->id << " forward: " << link->forward << std::endl;
            // }

            // finish print this path
            std::cout << "--------------------------------" << std::endl;
        }
    }
};

int main(int argc, char** argv) {
    std::string config_file = "/home/<USER>/dlc/map_engine/src/path/config/path.yaml";

    std::string uuid;
    if (argc >= 2) {
        // config_file = argv[1];
        uuid = argv[1];
    } else {
        std::cout << "Using default config file: " << config_file << std::endl;
    }

    std::filesystem::path config_path(config_file);
    if (!std::filesystem::exists(config_path)) {
        std::cerr << "Error: config file not found: " << config_file << std::endl;
        std::cerr << "Usage: " << argv[0] << " <path_config_yaml>" << std::endl;
        return -1;
    }

    if (!aurora::logger::get().init("logs/spdlog_demo.log")) {
        std::cerr << "Failed to initialize logger" << std::endl;
        return 1;
    }
    aurora::logger::get().set_level(spdlog::level::trace);
    std::cout << "path tool uuid: " << aurora::path::Uuid::GetUuid() << std::endl;

    GeoJsonWritter writer;
    // DirectEdgeInfoRawPtr edge_info = std::make_shared<DirectEdgeInfo>();
    // writer.WriteDirectEdgeInfo(edge_info, "edge_info.geojson");


    // create path module && init module
    std::shared_ptr<aurora::path::PathModule> path_module = std::make_shared<aurora::path::PathModule>();

    // set data_dir in parameters
    std::string data_dir = "/home/<USER>/dlc/map_engine/distribution/data/route";
    path_module->Prepare(config_file);
    path_module->SetParams({{"data_dir", data_dir}});
    auto module_finder = [](aurora::ModuleId id) {
        return nullptr; //std::dynamic_pointer_cast<aurora::Interface>(path_module);
    };  
    path_module->Init(module_finder);

    // start module
    path_module->Start();

    // get path interface
    std::shared_ptr<PathInterface> path_interface = std::dynamic_pointer_cast<PathInterface>(path_module->GetInterface());
    std::shared_ptr<PathListenerDemo> path_listener = std::make_shared<PathListenerDemo>();
    path_interface->AddPathListener(path_listener);

    auto path_query = std::make_shared<PathQuery>();
    // shanghai to beijing ,fill to path_query 
    auto start = std::make_shared<PathLandmark>();
    start->valid = true;
    start->waypoint_type = WayPointType::kStartPoint;
    auto end = std::make_shared<PathLandmark>();
    end->valid = true;
    end->waypoint_type = WayPointType::kEndPoint;
    // start->pt = PointLL(121.455715, 31.207708);
    // end->pt = PointLL(121.491488, 31.239482);
    start->pt = PointLL(121.279628, 31.178582); // 上海美的全球创新园区
    start->pt = PointLL(121.36843, 31.19032); // 动物园
    start->pt = PointLL(121.4692, 31.2204); // haerbin
    // start->pt = PointLL(116.398165, 39.907708); // beijing
    // end->pt = PointLL(121.29359, 31.18655);

    // start->pt = PointLL(121.589402, 31.08731); // putong

    //end->pt = PointLL(121.30160, 31.20726); // 5km in one tile

    //end->pt = PointLL(121.2763, 31.1456);  // cross tile

    end->pt = PointLL(121.9569, 30.8891);  // super long path 85km, 滴水湖
    end->pt = PointLL(121.4267, 31.1567);  //shenzhen
    // path_query->end->pt = PointLL(121.4378, 31.0449);  // super long path 40km
    // end->pt = PointLL(121.574169, 31.160323);  // 御桥地铁站
    // end->pt = PointLL(121.566608, 31.169101);  // 莲溪路
    // end->pt = PointLL(121.7071570, 31.0590819);  // 野生动物园

    // end->pt = PointLL(121.367483, 31.190336); // the same edge as start
    // end->pt = PointLL(121.36843, 31.19032);

    auto waypoint = std::make_shared<PathLandmark>();
    waypoint->valid = true;
    waypoint->waypoint_type = WayPointType::kViaPoint;
    // waypoint->pt = PointLL(121.30160, 31.20726); // 中间点 (waypoint)
    waypoint->pt = PointLL(121.30661, 31.18227); // 中间点 (waypoint)

    path_query->path_points.push_back(start);
    // path_query->path_points.push_back(waypoint);
    path_query->path_points.push_back(end);

    std::cout << "Testing waypoint routing with " << path_query->path_points.size() << " points:" << std::endl;
    std::cout << "Start: " << start->pt.lng() << ", " << start->pt.lat() << std::endl;
    std::cout << "Waypoint: " << waypoint->pt.lng() << ", " << waypoint->pt.lat() << std::endl;
    std::cout << "End: " << end->pt.lng() << ", " << end->pt.lat() << std::endl;

    if (path_interface == nullptr) {
        std::cout << "path_interface is nullptr" << std::endl;
        return -1;
    }

    std::string query_file = "/mnt/d/osm/map_engine/debug/path_query.geojson";
    if (!uuid.empty()) {
        query_file = "/mnt/d/osm/map_engine/debug/test_cases/" + uuid + "/path_query.geojson";
    }

    auto new_path_query = GeoJsonReader::ReadPathQuery(query_file);

    path_interface->RequestPath(path_query);
    // path_interface->RequestPath(new_path_query);

    // stop module
    path_module->Stop();

    // uninit module
    path_interface->RemovePathListener(path_listener);
    path_module->UnInit();




    path_module->Prepare(config_file);
    path_module->SetParams({{"data_dir", data_dir}});

    // auto module_finder = [](aurora::ModuleId id) {
    //     return nullptr; //std::dynamic_pointer_cast<aurora::Interface>(path_module);
    // };
    path_module->Init(module_finder);

    // start module
    path_module->Start();

    // get path interface
    // std::shared_ptr<PathInterface> path_interface = std::dynamic_pointer_cast<PathInterface>(path_module->GetInterface());
    // std::shared_ptr<PathListenerDemo> path_listener = std::make_shared<PathListenerDemo>();
    path_interface->AddPathListener(path_listener);

    path_query = std::make_shared<PathQuery>();
    // shanghai to beijing ,fill to path_query 
    start = std::make_shared<PathLandmark>();
    start->valid = true;
    start->waypoint_type = WayPointType::kStartPoint;
    end = std::make_shared<PathLandmark>();
    end->valid = true;
    end->waypoint_type = WayPointType::kEndPoint;
    // start->pt = PointLL(121.455715, 31.207708);
    // end->pt = PointLL(121.491488, 31.239482);
    start->pt = PointLL(121.279628, 31.178582); // 上海美的全球创新园区
    start->pt = PointLL(121.36843, 31.19032); // 动物园
    start->pt = PointLL(121.4692, 31.2204); // haerbin
    // start->pt = PointLL(116.398165, 39.907708); // beijing
    // end->pt = PointLL(121.29359, 31.18655);

    // start->pt = PointLL(121.589402, 31.08731); // putong

    //end->pt = PointLL(121.30160, 31.20726); // 5km in one tile

    //end->pt = PointLL(121.2763, 31.1456);  // cross tile

    end->pt = PointLL(121.9569, 30.8891);  // super long path 85km, 滴水湖
    end->pt = PointLL(121.4267, 31.1567);  //shenzhen
    // path_query->end->pt = PointLL(121.4378, 31.0449);  // super long path 40km
    // end->pt = PointLL(121.574169, 31.160323);  // 御桥地铁站
    // end->pt = PointLL(121.566608, 31.169101);  // 莲溪路
    // end->pt = PointLL(121.7071570, 31.0590819);  // 野生动物园

    // end->pt = PointLL(121.367483, 31.190336); // the same edge as start
    // end->pt = PointLL(121.36843, 31.19032);

    waypoint = std::make_shared<PathLandmark>();
    waypoint->valid = true;
    waypoint->waypoint_type = WayPointType::kViaPoint;
    // waypoint->pt = PointLL(121.30160, 31.20726); // 中间点 (waypoint)
    waypoint->pt = PointLL(121.30661, 31.18227); // 中间点 (waypoint)

    path_query->path_points.push_back(start);
    // path_query->path_points.push_back(waypoint);
    path_query->path_points.push_back(end);

    std::cout << "Testing waypoint routing with " << path_query->path_points.size() << " points:" << std::endl;
    std::cout << "Start: " << start->pt.lng() << ", " << start->pt.lat() << std::endl;
    std::cout << "Waypoint: " << waypoint->pt.lng() << ", " << waypoint->pt.lat() << std::endl;
    std::cout << "End: " << end->pt.lng() << ", " << end->pt.lat() << std::endl;

    if (path_interface == nullptr) {
        std::cout << "path_interface is nullptr" << std::endl;
        return -1;
    }

    new_path_query = GeoJsonReader::ReadPathQuery(query_file);

    path_interface->RequestPath(path_query);
    // path_interface->RequestPath(new_path_query);

    // stop module
    path_module->Stop();

    // uninit module
    path_interface->RemovePathListener(path_listener);
    path_module->UnInit();

    aurora::logger::get().shutdown();
    return 0;
}
