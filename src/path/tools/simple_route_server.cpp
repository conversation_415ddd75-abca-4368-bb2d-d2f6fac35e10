// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// 简单的HTTP服务器封装C++算路功能，供JavaScript调用
//

#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <sstream>
#include <string>
#include <map>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <cstring>
#include <sys/socket.h>
#include <netinet/in.h>
#include <unistd.h>
#include <signal.h>
#include <regex>

#include "base/include/module.h"
#include "path/src/base/uuid.h"
#include "path_module.h"
#include "pointll.h"
#include "logger.h"

// 简单的日志宏定义
#define LOG_INFO(fmt, ...) std::cout << "[INFO] " << fmt << std::endl

using namespace aurora::path;
using namespace aurora;

// 简单的JSON解析器
class SimpleJSON {
public:
    static double getDouble(const std::string& json, const std::string& key) {
        std::regex pattern("\"" + key + "\"\\s*:\\s*([0-9.-]+)");
        std::smatch match;
        if (std::regex_search(json, match, pattern)) {
            try {
                return std::stod(match[1].str());
            } catch (const std::exception& e) {
                std::cerr << "Failed to parse double for key " << key << ": " << e.what() << std::endl;
                return 0.0;
            }
        }
        std::cerr << "Key not found: " << key << std::endl;
        return 0.0;
    }

    static int getInt(const std::string& json, const std::string& key, int defaultValue = 0) {
        std::regex pattern("\"" + key + "\"\\s*:\\s*([0-9-]+)");
        std::smatch match;
        if (std::regex_search(json, match, pattern)) {
            try {
                return std::stoi(match[1].str());
            } catch (const std::exception& e) {
                std::cerr << "Failed to parse int for key " << key << ": " << e.what() << std::endl;
                return defaultValue;
            }
        }
        return defaultValue;
    }

    static std::string getString(const std::string& json, const std::string& key) {
        std::regex pattern("\"" + key + "\"\\s*:\\s*\"([^\"]+)\"");
        std::smatch match;
        if (std::regex_search(json, match, pattern)) {
            return match[1].str();
        }
        return "";
    }

    // 解析途经点数组
    static std::vector<std::pair<double, double>> getWaypoints(const std::string& json) {
        std::vector<std::pair<double, double>> waypoints;

        // 查找waypoints数组
        std::regex waypoints_pattern("\"waypoints\"\\s*:\\s*\\[(.*?)\\]");
        std::smatch waypoints_match;

        if (std::regex_search(json, waypoints_match, waypoints_pattern)) {
            std::string waypoints_str = waypoints_match[1].str();

            // 解析每个途经点对象 {"lng":xxx,"lat":xxx}
            std::regex point_pattern("\\{\\s*\"lng\"\\s*:\\s*([0-9.-]+)\\s*,\\s*\"lat\"\\s*:\\s*([0-9.-]+)\\s*\\}");
            std::sregex_iterator iter(waypoints_str.begin(), waypoints_str.end(), point_pattern);
            std::sregex_iterator end;

            for (; iter != end; ++iter) {
                try {
                    double lng = std::stod((*iter)[1].str());
                    double lat = std::stod((*iter)[2].str());
                    waypoints.push_back(std::make_pair(lng, lat));
                } catch (const std::exception& e) {
                    std::cerr << "Failed to parse waypoint: " << e.what() << std::endl;
                }
            }
        }

        return waypoints;
    }
};

class SimpleRouteServer {
private:
    std::shared_ptr<aurora::path::PathModule> path_module_;
    std::shared_ptr<PathInterface> path_interface_;
    std::shared_ptr<PathListener> path_listener_;
    std::string config_file_;
    std::string data_dir_;
    bool initialized_;
    std::atomic<bool> running_;
    int server_socket_;

    // 路径结果存储
    struct RouteResult {
        bool completed = false;
        PathResultPtr result = nullptr;
        std::string error_message;
        std::chrono::steady_clock::time_point start_time;
    };
    
    std::map<std::string, RouteResult> pending_results_;
    std::mutex results_mutex_;
    std::condition_variable results_cv_;

public:
    SimpleRouteServer(const std::string& config_file, const std::string& data_dir) 
        : config_file_(config_file), data_dir_(data_dir), initialized_(false), running_(false), server_socket_(-1) {}

    ~SimpleRouteServer() {
        StopServer();
    }

    bool Initialize() {
        try {
            // 初始化日志
            if (!aurora::logger::get().init("logs/route_server.log")) {
                std::cerr << "Failed to initialize logger" << std::endl;
                return false;
            }
            aurora::logger::get().set_level(spdlog::level::info);

            // 创建并初始化路径模块
            path_module_ = std::make_shared<aurora::path::PathModule>();
            path_module_->Prepare(config_file_);
            path_module_->SetParams({{"data_dir", data_dir_}});

            auto module_finder = [](aurora::ModuleId id) {
                return nullptr;
            };
            path_module_->Init(module_finder);
            path_module_->Start();

            // 获取路径接口
            path_interface_ = std::dynamic_pointer_cast<PathInterface>(path_module_->GetInterface());
            if (!path_interface_) {
                std::cerr << "Failed to get path interface" << std::endl;
                return false;
            }

            // 创建路径监听器
            path_listener_ = std::make_shared<RouteListener>(this);
            path_interface_->AddPathListener(path_listener_);

            initialized_ = true;
            std::cout << "Route server initialized successfully" << std::endl;
            return true;
        } catch (const std::exception& e) {
            std::cerr << "Failed to initialize route server: " << e.what() << std::endl;
            return false;
        }
    }

    bool StartServer(int port) {
        if (!initialized_) {
            std::cerr << "Server not initialized" << std::endl;
            return false;
        }

        // 创建socket
        server_socket_ = socket(AF_INET, SOCK_STREAM, 0);
        if (server_socket_ < 0) {
            std::cerr << "Failed to create socket" << std::endl;
            return false;
        }

        // 设置socket选项
        int opt = 1;
        setsockopt(server_socket_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

        // 绑定地址
        struct sockaddr_in address;
        address.sin_family = AF_INET;
        address.sin_addr.s_addr = INADDR_ANY;
        address.sin_port = htons(port);

        if (bind(server_socket_, (struct sockaddr*)&address, sizeof(address)) < 0) {
            std::cerr << "Failed to bind socket to port " << port << std::endl;
            close(server_socket_);
            return false;
        }

        // 开始监听
        if (listen(server_socket_, 10) < 0) {
            std::cerr << "Failed to listen on socket" << std::endl;
            close(server_socket_);
            return false;
        }

        running_ = true;
        std::cout << "Route server listening on port " << port << std::endl;

        // 处理连接
        while (running_) {
            struct sockaddr_in client_address;
            socklen_t client_len = sizeof(client_address);
            
            int client_socket = accept(server_socket_, (struct sockaddr*)&client_address, &client_len);
            if (client_socket < 0) {
                if (running_) {
                    std::cerr << "Failed to accept connection" << std::endl;
                }
                continue;
            }

            // 在新线程中处理请求
            std::thread([this, client_socket]() {
                HandleRequest(client_socket);
                close(client_socket);
            }).detach();
        }

        return true;
    }

    void StopServer() {
        running_ = false;
        if (server_socket_ >= 0) {
            close(server_socket_);
            server_socket_ = -1;
        }
        if (path_interface_ && path_listener_) {
            path_interface_->RemovePathListener(path_listener_);
        }
        if (path_module_) {
            path_module_->Stop();
            path_module_->UnInit();
        }
        aurora::logger::get().shutdown();
    }

private:
    void HandleRequest(int client_socket) {
        char buffer[4096];
        ssize_t bytes_read = recv(client_socket, buffer, sizeof(buffer) - 1, 0);
        
        if (bytes_read <= 0) {
            return;
        }
        
        buffer[bytes_read] = '\0';
        std::string request(buffer);
        
        // 解析HTTP请求
        std::istringstream iss(request);
        std::string method, path, version;
        iss >> method >> path >> version;
        
        // CORS预检请求
        if (method == "OPTIONS") {
            SendCORSResponse(client_socket);
            return;
        }
        
        // 处理POST请求
        if (method == "POST") {
            if (path == "/route") {
                HandleRouteRequest(client_socket, request);
            } else {
                SendErrorResponse(client_socket, 404, "Not Found");
            }
        } else if (method == "GET") {
            if (path.substr(0, 8) == "/result/") {
                std::string uuid = path.substr(8);
                HandleResultRequest(client_socket, uuid);
            } else {
                SendErrorResponse(client_socket, 404, "Not Found");
            }
        } else {
            SendErrorResponse(client_socket, 405, "Method Not Allowed");
        }
    }

    void SendCORSResponse(int client_socket) {
        std::string response = 
            "HTTP/1.1 200 OK\r\n"
            "Access-Control-Allow-Origin: *\r\n"
            "Access-Control-Allow-Methods: POST, GET, OPTIONS\r\n"
            "Access-Control-Allow-Headers: Content-Type\r\n"
            "Content-Length: 0\r\n"
            "\r\n";
        send(client_socket, response.c_str(), response.length(), 0);
    }

    void SendErrorResponse(int client_socket, int code, const std::string& message) {
        std::string json_body = "{\"error\":\"" + message + "\"}";
        std::string response =
            "HTTP/1.1 " + std::to_string(code) + " " + message + "\r\n"
            "Access-Control-Allow-Origin: *\r\n"
            "Content-Type: application/json\r\n"
            "Content-Length: " + std::to_string(json_body.length()) + "\r\n"
            "\r\n" + json_body;
        send(client_socket, response.c_str(), response.length(), 0);
    }

    void SendJSONResponse(int client_socket, const std::string& json) {
        std::string response = 
            "HTTP/1.1 200 OK\r\n"
            "Access-Control-Allow-Origin: *\r\n"
            "Content-Type: application/json\r\n"
            "Content-Length: " + std::to_string(json.length()) + "\r\n"
            "\r\n" + json;
        LOG_INFO("response:{}", response);
        send(client_socket, response.c_str(), response.length(), 0);
    }

    void HandleRouteRequest(int client_socket, const std::string& request) {
        try {
            std::cout << "Handling route request..." << std::endl;

            // 提取JSON body
            size_t body_start = request.find("\r\n\r\n");
            if (body_start == std::string::npos) {
                std::cout << "Invalid request: no body found" << std::endl;
                SendErrorResponse(client_socket, 400, "Invalid request");
                return;
            }

            std::string json_body = request.substr(body_start + 4);
            std::cout << "Request body: " << json_body << std::endl;

            // 解析坐标
            double start_lng = SimpleJSON::getDouble(json_body, "start_lng");
            double start_lat = SimpleJSON::getDouble(json_body, "start_lat");
            double end_lng = SimpleJSON::getDouble(json_body, "end_lng");
            double end_lat = SimpleJSON::getDouble(json_body, "end_lat");

            // 解析途经点
            std::vector<std::pair<double, double>> waypoints = SimpleJSON::getWaypoints(json_body);

            // 解析算路策略
            int strategy = SimpleJSON::getInt(json_body, "strategy", 0);

            std::cout << "Parsed coordinates: start(" << start_lng << ", " << start_lat
                      << ") end(" << end_lng << ", " << end_lat << ") strategy(" << strategy << ")";
            if (!waypoints.empty()) {
                std::cout << " with " << waypoints.size() << " waypoints";
                for (size_t i = 0; i < waypoints.size(); ++i) {
                    std::cout << " wp" << (i+1) << "(" << waypoints[i].first << ", " << waypoints[i].second << ")";
                }
            }
            std::cout << std::endl;

            if (start_lng == 0.0 || start_lat == 0.0 || end_lng == 0.0 || end_lat == 0.0) {
                std::cout << "Invalid coordinates detected" << std::endl;
                SendErrorResponse(client_socket, 400, "Invalid coordinates");
                return;
            }

            // 执行算路（支持途经点和策略）
            std::string uuid = CalculateRoute(start_lng, start_lat, end_lng, end_lat, waypoints, strategy);
            std::cout << "Route calculation started with UUID: " << uuid << std::endl;

            // 返回结果
            std::string response_json = "{\"uuid\":\"" + uuid + "\",\"status\":\"processing\"}";
            SendJSONResponse(client_socket, response_json);

        } catch (const std::exception& e) {
            std::cout << "Route request error: " << e.what() << std::endl;
            SendErrorResponse(client_socket, 500, e.what());
        }
    }

    void HandleResultRequest(int client_socket, const std::string& uuid) {
        std::cout << "Handling result request for UUID: " << uuid << std::endl;
        std::string result_json = GetRouteResultJSON(uuid);
        // std::cout << "Result JSON: " << result_json << std::endl;
        SendJSONResponse(client_socket, result_json);
    }

    std::string CalculateRoute(double start_lng, double start_lat, double end_lng, double end_lat,
                              const std::vector<std::pair<double, double>>& waypoints = {}, int strategy = 0) {
        // 创建路径查询
        auto path_query = std::make_shared<PathQuery>();

        // 设置算路策略
        path_query->strategy = static_cast<PathStrategy>(strategy);
        path_query->trigger = PathTrigger::kInitialRouting;
        path_query->mode = PathMode::kOffline;
        path_query->date_option.type = DateTimeType::kCurrent;

        // 设置起点
        auto start = std::make_shared<PathLandmark>();
        start->valid = true;
        start->waypoint_type = WayPointType::kStartPoint;
        start->landmark_type = LandmarkType::kClick;
        start->pt = PointLL(start_lng, start_lat);
        path_query->path_points.push_back(start);

        // 添加途经点（按顺序）
        for (const auto& wp : waypoints) {
            auto waypoint = std::make_shared<PathLandmark>();
            waypoint->valid = true;
            waypoint->waypoint_type = WayPointType::kViaPoint;
            waypoint->landmark_type = LandmarkType::kClick;
            waypoint->pt = PointLL(wp.first, wp.second);
            path_query->path_points.push_back(waypoint);
        }

        // 设置终点
        auto end = std::make_shared<PathLandmark>();
        end->valid = true;
        end->waypoint_type = WayPointType::kEndPoint;
        end->landmark_type = LandmarkType::kClick;
        end->pt = PointLL(end_lng, end_lat);
        path_query->path_points.push_back(end);

        // 策略名称映射
        const char* strategy_names[] = {"TimeFirst", "DistanceFirst", "HighwayFirst", "AvoidToll"};
        const char* strategy_name = (strategy >= 0 && strategy < 4) ? strategy_names[strategy] : "Unknown";

        std::cout << "Creating route with " << path_query->path_points.size() << " points (Strategy: " << strategy_name << "):" << std::endl;
        std::cout << "  Start: (" << start_lng << ", " << start_lat << ")" << std::endl;
        for (size_t i = 0; i < waypoints.size(); ++i) {
            std::cout << "  Waypoint " << (i+1) << ": (" << waypoints[i].first << ", " << waypoints[i].second << ")" << std::endl;
        }
        std::cout << "  End: (" << end_lng << ", " << end_lat << ")" << std::endl;

        // 请求路径
        std::string uuid = path_interface_->RequestPath(path_query);

        // 初始化结果存储
        {
            std::lock_guard<std::mutex> lock(results_mutex_);
            pending_results_[uuid] = RouteResult();
            pending_results_[uuid].start_time = std::chrono::steady_clock::now();
        }

        return uuid;
    }

    std::string GetRouteResultJSON(const std::string& uuid) {
        std::lock_guard<std::mutex> lock(results_mutex_);
        auto it = pending_results_.find(uuid);
        
        if (it == pending_results_.end()) {
            return "{\"status\":\"not_found\"}";
        }

        if (!it->second.completed) {
            // 检查超时
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - it->second.start_time);
            if (elapsed.count() > 30) { // 30秒超时
                pending_results_.erase(it);
                return "{\"status\":\"timeout\"}";
            }
            return "{\"status\":\"processing\"}";
        }

        if (!it->second.result || it->second.result->paths.empty()) {
            std::string error_msg = it->second.error_message.empty() ? "No route found" : it->second.error_message;
            pending_results_.erase(it);
            return "{\"status\":\"failed\",\"error\":\"" + error_msg + "\"}";
        }

        // 构建成功响应
        auto result = it->second.result;

        std::ostringstream json;
        json << "{";
        json << "\"status\":\"completed\",";
        json << "\"uuid\":\"" << result->uuid << "\",";
        json << "\"code\":" << result->code << ",";
        json << "\"tag\":\"" << result->tag << "\",";
        json << "\"query_time_ms\":" << result->metadata.query_time_ms << ",";
        json << "\"paths\":[";

        // 遍历所有路线
        for (size_t pathIndex = 0; pathIndex < result->paths.size(); ++pathIndex) {
            if (pathIndex > 0) json << ",";

            const auto& path = result->paths[pathIndex];
            json << "{";
            json << "\"path_id\":" << path.path_id << ",";
            json << "\"length\":" << std::fixed << std::setprecision(2) << path.length << ",";
            json << "\"travel_time\":" << std::fixed << std::setprecision(2) << path.travel_time << ",";
            json << "\"traffic_light_num\":" << path.traffic_light_num << ",";
            json << "\"points\":[";

            for (size_t i = 0; i < path.points.size(); ++i) {
                if (i > 0) json << ",";
                json << "{\"lng\":" << std::fixed << std::setprecision(6) << path.points[i].lng()
                     << ",\"lat\":" << std::fixed << std::setprecision(6) << path.points[i].lat() << "}";
            }

            json << "]}";
        }

        json << "]}";

        // 清理结果
        pending_results_.erase(it);

        return json.str();
    }

    // 路径监听器类
    class RouteListener : public PathListener {
    private:
        SimpleRouteServer* server_;
    public:
        RouteListener(SimpleRouteServer* server) : server_(server) {}
        
        void OnPathResult(const PathQueryPtr& query, const PathResultPtr& result) override {
            LOG_INFO("uuid:{}", result->uuid);

            std::cout << "Path result uuid: " << result->uuid << std::endl;
            std::cout << "Path result status: " << result->status << std::endl;
            std::cout << "Path result code: " << result->code << std::endl;
            std::cout << "Path result tag: " << result->tag << std::endl;
            std::cout << "Path result query_time_ms: " << result->metadata.query_time_ms << std::endl;
            std::cout << "Path result paths: " << result->paths.size() << std::endl;
            for (const auto& path : result->paths) {
                std::cout << "Path result path: " << path.path_id << std::endl;
                std::cout << "Path result path length: " << path.length << std::endl;
                std::cout << "Path result path travel time: " << path.travel_time << std::endl;
                std::cout << "Path result traffic_light: " << path.traffic_light_num << std::endl;
                std::cout << "Path result path sections: " << path.sections.size() << std::endl;
                for (const auto& section : path.sections) {
                    std::cout << "Path result path section start index: " << section.index << std::endl;
                    std::cout << "Path result path section num: " << section.num << std::endl;
                    std::cout << "Path result path section length: " << section.length << std::endl;
                    std::cout << "Path result path section time: " << section.time << std::endl;
                    std::cout << "Path result path section start_offset: " << section.start_offset << std::endl;
                    std::cout << "Path result path section end_offset: " << section.end_offset << std::endl;
                }

                // start print path links
                std::cout << "Path result path links: " << path.links.size() << std::endl;
                // finish print this path
                std::cout << "--------------------------------" << std::endl;
            }
            std::lock_guard<std::mutex> lock(server_->results_mutex_);
            auto it = server_->pending_results_.find(result->uuid);
            if (it != server_->pending_results_.end()) {
                LOG_INFO("response match req, uuid:{}", result->uuid);
                it->second.completed = true;
                it->second.result = result;
                if (result->code != 0) {
                    it->second.error_message = "Route calculation failed with code: " + std::to_string(result->code);
                }
            }
            server_->results_cv_.notify_all();
        }
    };
};

// 全局服务器实例
std::unique_ptr<SimpleRouteServer> g_server;

void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    if (g_server) {
        g_server->StopServer();
    }
    exit(0);
}

int main(int argc, char** argv) {
    std::string config_file = "/home/<USER>/dlc/map_engine/src/path/config/path.yaml";
    std::string data_dir = "/home/<USER>/dlc/map_engine/distribution/data/route";
    int port = 8080;

    if (argc >= 2) {
        config_file = argv[1];
    }
    if (argc >= 3) {
        data_dir = argv[2];
    }
    if (argc >= 4) {
        port = std::atoi(argv[3]);
    }

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    g_server = std::make_unique<SimpleRouteServer>(config_file, data_dir);
    
    if (!g_server->Initialize()) {
        std::cerr << "Failed to initialize route server" << std::endl;
        return -1;
    }

    std::cout << "Starting route server on port " << port << std::endl;
    std::cout << "Config file: " << config_file << std::endl;
    std::cout << "Data directory: " << data_dir << std::endl;
    std::cout << "Press Ctrl+C to stop the server" << std::endl;
    
    if (!g_server->StartServer(port)) {
        std::cerr << "Failed to start server" << std::endl;
        return -1;
    }

    return 0;
}
