// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-17
//
// 云端算路客户端类，用于通过HTTP请求访问云端算路服务

#ifndef AURORA_PATH_SRC_ROUTE_ALGORITHM_CLOUD_ROUTE_CLIENT_H_
#define AURORA_PATH_SRC_ROUTE_ALGORITHM_CLOUD_ROUTE_CLIENT_H_

#include <string>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <unordered_map>
#include <chrono>

#include "path_def.h"

namespace aurora {
namespace path {

/**
 * 云端算路请求结果状态
 */
enum class CloudRouteStatus {
    kProcessing,    // 处理中
    kCompleted,     // 完成
    kFailed,        // 失败
    kTimeout,       // 超时
    kNotFound       // 未找到
};

/**
 * 云端算路请求结果
 */
struct CloudRouteResult {
    CloudRouteStatus status;
    std::string uuid;
    std::string error_message;
    PathResultPtr path_result;
    std::chrono::steady_clock::time_point start_time;
    
    CloudRouteResult() : status(CloudRouteStatus::kProcessing) {
        start_time = std::chrono::steady_clock::now();
    }
};

/**
 * CloudRouteClient - 云端算路客户端
 * 
 * 负责与云端算路服务进行HTTP通信，提供异步算路功能
 * 参考simple_route_server.cpp的API接口设计
 */
class CloudRouteClient {
public:
    /**
     * 构造函数
     * @param server_url 云端算路服务器URL
     * @param timeout_seconds 请求超时时间（秒）
     */
    explicit CloudRouteClient(const std::string& server_url, int timeout_seconds = 30);
    
    /**
     * 析构函数
     */
    ~CloudRouteClient();
    
    // 禁用拷贝构造和赋值
    CloudRouteClient(const CloudRouteClient&) = delete;
    CloudRouteClient& operator=(const CloudRouteClient&) = delete;
    
    /**
     * 初始化客户端
     * @return 0表示成功，其他值表示失败
     */
    int Initialize();
    
    /**
     * 清理资源
     */
    void Cleanup();
    
    /**
     * 异步请求云端算路
     * @param query 路径查询请求
     * @param callback 结果回调函数
     * @return 请求UUID，用于跟踪请求状态
     */
    std::string RequestRouteAsync(const PathQuery& query, 
                                 std::function<void(const PathResultPtr&)> callback);
    
    /**
     * 同步请求云端算路
     * @param query 路径查询请求
     * @return 路径计算结果
     */
    PathResultPtr RequestRouteSync(const PathQuery& query);
    
    /**
     * 取消指定的算路请求
     * @param uuid 请求UUID
     * @return true表示成功取消，false表示请求不存在或已完成
     */
    bool CancelRequest(const std::string& uuid);
    
    /**
     * 获取请求状态
     * @param uuid 请求UUID
     * @return 请求状态
     */
    CloudRouteStatus GetRequestStatus(const std::string& uuid);
    
    /**
     * 设置服务器URL
     * @param server_url 新的服务器URL
     */
    void SetServerUrl(const std::string& server_url);
    
    /**
     * 设置超时时间
     * @param timeout_seconds 超时时间（秒）
     */
    void SetTimeout(int timeout_seconds);

private:
    /**
     * 发送HTTP POST请求
     * @param url 请求URL
     * @param json_data JSON数据
     * @param response 响应数据
     * @return true表示成功，false表示失败
     */
    bool SendHttpPost(const std::string& url, const std::string& json_data, std::string& response);
    
    /**
     * 发送HTTP GET请求
     * @param url 请求URL
     * @param response 响应数据
     * @return true表示成功，false表示失败
     */
    bool SendHttpGet(const std::string& url, std::string& response);
    
    /**
     * 将PathQuery转换为JSON字符串
     * @param query 路径查询请求
     * @return JSON字符串
     */
    std::string PathQueryToJson(const PathQuery& query);
    
    /**
     * 将JSON响应转换为PathResult
     * @param json_response JSON响应字符串
     * @return PathResult指针
     */
    PathResultPtr JsonToPathResult(const std::string& json_response);
    
    /**
     * 轮询结果的工作线程函数
     */
    void PollResultsWorker();
    
    /**
     * 处理单个请求的结果轮询
     * @param uuid 请求UUID
     * @param result 请求结果引用
     */
    void PollSingleResult(const std::string& uuid, CloudRouteResult& result);
    
    /**
     * 清理超时的请求
     */
    void CleanupTimeoutRequests();

private:
    std::string server_url_;                                    // 服务器URL
    int timeout_seconds_;                                       // 超时时间
    bool initialized_;                                          // 初始化状态
    
    std::unordered_map<std::string, CloudRouteResult> pending_requests_;  // 待处理请求
    std::unordered_map<std::string, std::function<void(const PathResultPtr&)>> callbacks_;  // 回调函数
    
    std::mutex requests_mutex_;                                 // 请求锁
    std::condition_variable requests_cv_;                       // 请求条件变量
    
    std::unique_ptr<std::thread> poll_thread_;                  // 轮询线程
    std::atomic<bool> stop_polling_;                            // 停止轮询标志
    
    static const int kMaxRetries = 3;                           // 最大重试次数
    static const int kPollIntervalMs = 100;                     // 轮询间隔（毫秒）
};

using CloudRouteClientPtr = std::unique_ptr<CloudRouteClient>;

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_ROUTE_ALGORITHM_CLOUD_ROUTE_CLIENT_H_
