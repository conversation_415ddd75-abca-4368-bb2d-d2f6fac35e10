// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-07-17
//
// 云端算路客户端实现

#include "cloud_route_client.h"

#include <curl/curl.h>
#include <sstream>
#include <iomanip>
#include <regex>
#include <thread>

#include "base/uuid.h"
#include "base/include/logger.h"
#include "base/include/errorcode.h"

namespace aurora {
namespace path {

// libcurl写回调函数
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* userp) {
    size_t total_size = size * nmemb;
    userp->append(static_cast<char*>(contents), total_size);
    return total_size;
}

CloudRouteClient::CloudRouteClient(const std::string& server_url, int timeout_seconds)
    : server_url_(server_url)
    , timeout_seconds_(timeout_seconds)
    , initialized_(false)
    , stop_polling_(false) {
}

CloudRouteClient::~CloudRouteClient() {
    Cleanup();
}

int CloudRouteClient::Initialize() {
    if (initialized_) {
        return 0;
    }
    
    // 初始化libcurl
    CURLcode res = curl_global_init(CURL_GLOBAL_DEFAULT);
    if (res != CURLE_OK) {
        LOG_ERROR("Failed to initialize libcurl: {}", curl_easy_strerror(res));
        return -1;
    }
    
    // 启动轮询线程
    stop_polling_ = false;
    poll_thread_ = std::make_unique<std::thread>(&CloudRouteClient::PollResultsWorker, this);
    
    initialized_ = true;
    LOG_INFO("CloudRouteClient initialized with server URL: {}", server_url_);
    return 0;
}

void CloudRouteClient::Cleanup() {
    if (!initialized_) {
        return;
    }
    
    // 停止轮询线程
    stop_polling_ = true;
    requests_cv_.notify_all();
    
    if (poll_thread_ && poll_thread_->joinable()) {
        poll_thread_->join();
    }
    
    // 清理libcurl
    curl_global_cleanup();
    
    // 清理待处理请求
    {
        std::lock_guard<std::mutex> lock(requests_mutex_);
        pending_requests_.clear();
        callbacks_.clear();
    }
    
    initialized_ = false;
    LOG_INFO("CloudRouteClient cleaned up");
}

std::string CloudRouteClient::RequestRouteAsync(const PathQuery& query, 
                                               std::function<void(const PathResultPtr&)> callback) {
    if (!initialized_) {
        LOG_ERROR("CloudRouteClient not initialized");
        return "";
    }
    
    // 生成UUID
    std::string uuid = UUID::Generate();
    
    // 转换为JSON
    std::string json_data = PathQueryToJson(query);
    
    // 发送请求到云端服务器
    std::string response;
    std::string route_url = server_url_ + "/route";
    
    if (!SendHttpPost(route_url, json_data, response)) {
        LOG_ERROR("Failed to send route request to server");
        return "";
    }
    
    // 解析响应获取服务器返回的UUID
    std::regex uuid_pattern("\"uuid\"\\s*:\\s*\"([^\"]+)\"");
    std::smatch match;
    std::string server_uuid;
    
    if (std::regex_search(response, match, uuid_pattern)) {
        server_uuid = match[1].str();
    } else {
        LOG_ERROR("Failed to parse UUID from server response: {}", response);
        return "";
    }
    
    // 存储请求信息
    {
        std::lock_guard<std::mutex> lock(requests_mutex_);
        pending_requests_[server_uuid] = CloudRouteResult();
        callbacks_[server_uuid] = callback;
    }
    
    // 通知轮询线程
    requests_cv_.notify_one();
    
    LOG_INFO("Async route request sent, server UUID: {}", server_uuid);
    return server_uuid;
}

PathResultPtr CloudRouteClient::RequestRouteSync(const PathQuery& query) {
    if (!initialized_) {
        LOG_ERROR("CloudRouteClient not initialized");
        return nullptr;
    }
    
    std::mutex sync_mutex;
    std::condition_variable sync_cv;
    PathResultPtr result = nullptr;
    bool completed = false;
    
    // 发送异步请求
    std::string uuid = RequestRouteAsync(query, [&](const PathResultPtr& path_result) {
        std::lock_guard<std::mutex> lock(sync_mutex);
        result = path_result;
        completed = true;
        sync_cv.notify_one();
    });
    
    if (uuid.empty()) {
        return nullptr;
    }
    
    // 等待结果
    std::unique_lock<std::mutex> lock(sync_mutex);
    sync_cv.wait_for(lock, std::chrono::seconds(timeout_seconds_), [&] { return completed; });
    
    if (!completed) {
        LOG_ERROR("Sync route request timeout for UUID: {}", uuid);
        CancelRequest(uuid);
        return nullptr;
    }
    
    return result;
}

bool CloudRouteClient::CancelRequest(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = pending_requests_.find(uuid);
    if (it == pending_requests_.end()) {
        return false;
    }
    
    pending_requests_.erase(it);
    callbacks_.erase(uuid);
    
    LOG_INFO("Cancelled route request: {}", uuid);
    return true;
}

CloudRouteStatus CloudRouteClient::GetRequestStatus(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(requests_mutex_);
    
    auto it = pending_requests_.find(uuid);
    if (it == pending_requests_.end()) {
        return CloudRouteStatus::kNotFound;
    }
    
    return it->second.status;
}

void CloudRouteClient::SetServerUrl(const std::string& server_url) {
    server_url_ = server_url;
    LOG_INFO("Server URL updated to: {}", server_url_);
}

void CloudRouteClient::SetTimeout(int timeout_seconds) {
    timeout_seconds_ = timeout_seconds;
    LOG_INFO("Timeout updated to: {} seconds", timeout_seconds_);
}

bool CloudRouteClient::SendHttpPost(const std::string& url, const std::string& json_data, std::string& response) {
    CURL* curl = curl_easy_init();
    if (!curl) {
        LOG_ERROR("Failed to initialize CURL");
        return false;
    }
    
    response.clear();
    
    // 设置URL
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    
    // 设置POST数据
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_data.c_str());
    
    // 设置HTTP头
    struct curl_slist* headers = nullptr;
    headers = curl_slist_append(headers, "Content-Type: application/json");
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    
    // 设置写回调
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    
    // 设置超时
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout_seconds_);
    
    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    
    // 清理
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    
    if (res != CURLE_OK) {
        LOG_ERROR("HTTP POST request failed: {}", curl_easy_strerror(res));
        return false;
    }
    
    return true;
}

bool CloudRouteClient::SendHttpGet(const std::string& url, std::string& response) {
    CURL* curl = curl_easy_init();
    if (!curl) {
        LOG_ERROR("Failed to initialize CURL");
        return false;
    }
    
    response.clear();
    
    // 设置URL
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
    
    // 设置写回调
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    
    // 设置超时
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, timeout_seconds_);
    
    // 执行请求
    CURLcode res = curl_easy_perform(curl);
    
    // 清理
    curl_easy_cleanup(curl);
    
    if (res != CURLE_OK) {
        LOG_ERROR("HTTP GET request failed: {}", curl_easy_strerror(res));
        return false;
    }
    
    return true;
}

std::string CloudRouteClient::PathQueryToJson(const PathQuery& query) {
    std::ostringstream json;

    // 获取起点和终点
    if (query.path_points.size() < 2) {
        LOG_ERROR("PathQuery must have at least 2 points");
        return "";
    }

    auto start_point = query.path_points[0];
    auto end_point = query.path_points.back();

    json << "{";
    json << "\"start_lng\":" << std::fixed << std::setprecision(6) << start_point->pt.lng() << ",";
    json << "\"start_lat\":" << std::fixed << std::setprecision(6) << start_point->pt.lat() << ",";
    json << "\"end_lng\":" << std::fixed << std::setprecision(6) << end_point->pt.lng() << ",";
    json << "\"end_lat\":" << std::fixed << std::setprecision(6) << end_point->pt.lat() << ",";
    json << "\"strategy\":" << static_cast<int>(query.strategy);

    // 添加途经点
    if (query.path_points.size() > 2) {
        json << ",\"waypoints\":[";
        for (size_t i = 1; i < query.path_points.size() - 1; ++i) {
            if (i > 1) json << ",";
            json << "{\"lng\":" << std::fixed << std::setprecision(6) << query.path_points[i]->pt.lng()
                 << ",\"lat\":" << std::fixed << std::setprecision(6) << query.path_points[i]->pt.lat() << "}";
        }
        json << "]";
    }

    json << "}";

    return json.str();
}

PathResultPtr CloudRouteClient::JsonToPathResult(const std::string& json_response) {
    auto result = std::make_shared<PathResult>();

    try {
        // 解析状态
        std::regex status_pattern("\"status\"\\s*:\\s*\"([^\"]+)\"");
        std::smatch match;

        if (std::regex_search(json_response, match, status_pattern)) {
            result->status = match[1].str();
        }

        // 如果状态不是completed，返回错误结果
        if (result->status != "completed") {
            result->code = static_cast<int>(ErrorCode::kErrorCodeFailed);

            // 尝试解析错误信息
            std::regex error_pattern("\"error\"\\s*:\\s*\"([^\"]+)\"");
            if (std::regex_search(json_response, match, error_pattern)) {
                LOG_ERROR("Cloud route error: {}", match[1].str());
            }

            return result;
        }

        // 解析UUID
        std::regex uuid_pattern("\"uuid\"\\s*:\\s*\"([^\"]+)\"");
        if (std::regex_search(json_response, match, uuid_pattern)) {
            result->uuid = match[1].str();
        }

        // 解析code
        std::regex code_pattern("\"code\"\\s*:\\s*([0-9]+)");
        if (std::regex_search(json_response, match, code_pattern)) {
            result->code = std::stoi(match[1].str());
        }

        // 解析tag
        std::regex tag_pattern("\"tag\"\\s*:\\s*\"([^\"]+)\"");
        if (std::regex_search(json_response, match, tag_pattern)) {
            result->tag = match[1].str();
        }

        // 解析query_time_ms
        std::regex time_pattern("\"query_time_ms\"\\s*:\\s*([0-9]+)");
        if (std::regex_search(json_response, match, time_pattern)) {
            result->metadata.query_time_ms = std::stoi(match[1].str());
        }

        // 解析路径数据
        std::regex paths_pattern("\"paths\"\\s*:\\s*\\[([^\\]]+)\\]");
        if (std::regex_search(json_response, match, paths_pattern)) {
            std::string paths_str = match[1].str();

            // 解析每条路径
            std::regex path_pattern("\\{([^\\}]+)\\}");
            std::sregex_iterator path_iter(paths_str.begin(), paths_str.end(), path_pattern);
            std::sregex_iterator path_end;

            for (; path_iter != path_end; ++path_iter) {
                std::string path_str = (*path_iter)[1].str();
                PathInfo path_info;

                // 解析path_id
                std::regex path_id_pattern("\"path_id\"\\s*:\\s*([0-9]+)");
                if (std::regex_search(path_str, match, path_id_pattern)) {
                    path_info.path_id = std::stoull(match[1].str());
                }

                // 解析length
                std::regex length_pattern("\"length\"\\s*:\\s*([0-9.]+)");
                if (std::regex_search(path_str, match, length_pattern)) {
                    path_info.length = std::stod(match[1].str());
                }

                // 解析travel_time
                std::regex travel_time_pattern("\"travel_time\"\\s*:\\s*([0-9.]+)");
                if (std::regex_search(path_str, match, travel_time_pattern)) {
                    path_info.travel_time = std::stod(match[1].str());
                }

                // 解析traffic_light_num
                std::regex light_pattern("\"traffic_light_num\"\\s*:\\s*([0-9]+)");
                if (std::regex_search(path_str, match, light_pattern)) {
                    path_info.traffic_light_num = std::stoi(match[1].str());
                }

                // 解析points
                std::regex points_pattern("\"points\"\\s*:\\s*\\[([^\\]]+)\\]");
                if (std::regex_search(path_str, match, points_pattern)) {
                    std::string points_str = match[1].str();

                    // 解析每个点
                    std::regex point_pattern("\\{\"lng\"\\s*:\\s*([0-9.-]+)\\s*,\\s*\"lat\"\\s*:\\s*([0-9.-]+)\\}");
                    std::sregex_iterator point_iter(points_str.begin(), points_str.end(), point_pattern);
                    std::sregex_iterator point_end;

                    for (; point_iter != point_end; ++point_iter) {
                        double lng = std::stod((*point_iter)[1].str());
                        double lat = std::stod((*point_iter)[2].str());
                        path_info.points.emplace_back(lng, lat);
                    }
                }

                result->paths.push_back(path_info);
            }
        }

        result->code = static_cast<int>(ErrorCode::kErrorCodeOk);

    } catch (const std::exception& e) {
        LOG_ERROR("Failed to parse JSON response: {}", e.what());
        result->status = "error";
        result->code = static_cast<int>(ErrorCode::kErrorCodeFailed);
    }

    return result;
}

void CloudRouteClient::PollResultsWorker() {
    LOG_INFO("Poll results worker thread started");

    while (!stop_polling_) {
        std::unique_lock<std::mutex> lock(requests_mutex_);

        // 等待有新请求或停止信号
        requests_cv_.wait_for(lock, std::chrono::milliseconds(kPollIntervalMs), [this] {
            return !pending_requests_.empty() || stop_polling_;
        });

        if (stop_polling_) {
            break;
        }

        // 处理所有待处理的请求
        auto requests_copy = pending_requests_;
        lock.unlock();

        for (auto& [uuid, result] : requests_copy) {
            if (result.status == CloudRouteStatus::kProcessing) {
                PollSingleResult(uuid, result);

                // 更新原始请求状态
                std::lock_guard<std::mutex> update_lock(requests_mutex_);
                auto it = pending_requests_.find(uuid);
                if (it != pending_requests_.end()) {
                    it->second = result;

                    // 如果请求完成，调用回调并清理
                    if (result.status != CloudRouteStatus::kProcessing) {
                        auto callback_it = callbacks_.find(uuid);
                        if (callback_it != callbacks_.end()) {
                            callback_it->second(result.path_result);
                            callbacks_.erase(callback_it);
                        }
                        pending_requests_.erase(it);
                    }
                }
            }
        }

        // 清理超时请求
        CleanupTimeoutRequests();
    }

    LOG_INFO("Poll results worker thread stopped");
}

void CloudRouteClient::PollSingleResult(const std::string& uuid, CloudRouteResult& result) {
    std::string result_url = server_url_ + "/result/" + uuid;
    std::string response;

    if (!SendHttpGet(result_url, response)) {
        LOG_ERROR("Failed to poll result for UUID: {}", uuid);
        return;
    }

    // 解析响应状态
    std::regex status_pattern("\"status\"\\s*:\\s*\"([^\"]+)\"");
    std::smatch match;

    if (std::regex_search(response, match, status_pattern)) {
        std::string status_str = match[1].str();

        if (status_str == "completed") {
            result.status = CloudRouteStatus::kCompleted;
            result.path_result = JsonToPathResult(response);
            LOG_INFO("Route request completed: {}", uuid);
        } else if (status_str == "failed") {
            result.status = CloudRouteStatus::kFailed;

            // 解析错误信息
            std::regex error_pattern("\"error\"\\s*:\\s*\"([^\"]+)\"");
            if (std::regex_search(response, match, error_pattern)) {
                result.error_message = match[1].str();
            }

            LOG_ERROR("Route request failed: {}, error: {}", uuid, result.error_message);
        } else if (status_str == "timeout") {
            result.status = CloudRouteStatus::kTimeout;
            LOG_ERROR("Route request timeout: {}", uuid);
        } else if (status_str == "not_found") {
            result.status = CloudRouteStatus::kNotFound;
            LOG_ERROR("Route request not found: {}", uuid);
        }
        // processing状态保持不变，继续轮询
    }
}

void CloudRouteClient::CleanupTimeoutRequests() {
    std::lock_guard<std::mutex> lock(requests_mutex_);

    auto now = std::chrono::steady_clock::now();
    auto it = pending_requests_.begin();

    while (it != pending_requests_.end()) {
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - it->second.start_time);

        if (elapsed.count() > timeout_seconds_) {
            LOG_WARN("Request timeout, removing: {}", it->first);

            // 调用回调通知超时
            auto callback_it = callbacks_.find(it->first);
            if (callback_it != callbacks_.end()) {
                callback_it->second(nullptr);  // 传递nullptr表示超时
                callbacks_.erase(callback_it);
            }

            it = pending_requests_.erase(it);
        } else {
            ++it;
        }
    }
}

}  // namespace path
}  // namespace aurora
