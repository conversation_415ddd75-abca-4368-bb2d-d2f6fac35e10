// Copyright (c) 2025 BYD Corporation. All rights reserved.
//
// Created by yang.wei121 on 2025-04-30
//

#include "path/src/path_interface_impl.h"
#include "path/src/base/uuid.h"
#include "Time.h"

#include <algorithm>
#include <chrono>

namespace aurora {
namespace path {

using MatchResultCallback = std::function<void(const loc::MatchResult&)>;
// impl MapMatchingListener
class MapMatchingListenerImpl : public loc::MapMatchingListener {
   public:
    MapMatchingListenerImpl(MatchResultCallback callback) : callback_(callback) {
    }

    void OnMapMatchingResult(const loc::MatchResult& result) override {
        // LOG_INFO("Map matching result stamp: {}", result.timestamp);
        callback_(result);
    }
private:
    MatchResultCallback callback_;
};

PathInterfaceImpl::PathInterfaceImpl() {
    // Initialize the path thread
    path_thread_ = std::make_unique<LoopThread>("PathCalculationThread");
    debug_manager_ = std::make_unique<DebugManager>();

    current_executing_uuid_ = "";
}

int PathInterfaceImpl::Prepare(const std::string& config) {
    // Load configuration from YAML file
    // std::string config_file = "src/path/config/path.yaml";
    // if (!config.empty()) {
    //     config_file = config;
    // }

    // Initialize the configuration manager with the config file
    if (!PathConfigManager::Initialize(config)) {
        LOG_ERROR("Failed to load configuration file: {}", config);
        return static_cast<int>(ErrorCode::kErrorCodeFailed);;
    }

    LOG_INFO("Successfully loaded configuration from: {}", config);

    // Initialize graph reader
    graph_reader_ptr_ = std::make_shared<GraphReader>();

    // Get configuration values from PathConfig
    const auto& config_manager = PathConfigManager::Instance();

    path_engine_ = std::make_unique<PathEngine>(graph_reader_ptr_);

    // 初始化云端算路客户端
    std::string cloud_server_url = config_manager.GetCloudRouteServerUrl();
    int cloud_timeout = config_manager.GetCloudRouteTimeout();
    cloud_route_client_ = std::make_unique<CloudRouteClient>(cloud_server_url, cloud_timeout);
    if (cloud_route_client_->Initialize() != 0) {
        LOG_WARN("Failed to initialize cloud route client, online routing will not be available");
        cloud_route_client_.reset();
    }

    map_matching_manager_ = std::make_unique<MapMatchingManager>(graph_reader_ptr_);

    debug_manager_->Initialize();
    return 0;
}

int32_t PathInterfaceImpl::SetParams(const ParameterMap &parameters) {
    // get value
    try {
        auto dir_pth = std::get<std::string>(parameters.at("data_dir"));
        LOG_INFO("dir_path:{}", dir_pth);
    } catch (const std::bad_variant_access&) {
        // return default value
        LOG_ERROR("dir_path ser error");
    }

    PathConfigManager::Instance().SetParams(parameters);
    return 0;
}

std::shared_ptr<IInterface> PathInterfaceImpl::GetInterface() {
    return shared_from_this();
}

int PathInterfaceImpl::Init(InterfaceFinder finder) {
    // TODO: Implement initialization logic
    // step1.get map_matching interface (optional for now)
    auto map_matching_interface = finder(ModuleId::kModuleIdLocation);
    if (!map_matching_interface) {
        // Location module is optional for now, just log a warning
        LOG_WARN("Location module not found, map matching will not be available");
    } else {
        LOG_INFO("=====> Location module is OK");
        loc_ = std::dynamic_pointer_cast<loc::ILocation>(map_matching_interface);
        if (loc_ == nullptr) {
            LOG_ERROR("Failed to get location interface");
            return static_cast<int>(ErrorCode::kErrorCodeFailed);
        }
        // 初始化地图匹配
        map_matching_manager_->Initialize(loc_);
        // map_matching_interface->RegistMapMatchingCallback(
        //     std::bind(&PathModuleImpl::OnMapMatchingCallback, this, std::placeholders::_1));
    }
    return 0;
}

int PathInterfaceImpl::Start() {
    // Start the path thread
    if (path_thread_) {
        path_thread_->Start();
    }
    return 0;
}

int PathInterfaceImpl::Stop() {
    // Stop the path thread
    if (path_thread_) {
        path_thread_->Stop();
    }
    return 0;
}

int PathInterfaceImpl::UnInit() {
    listeners_.clear();
    return 0;
}

ModuleId PathInterfaceImpl::GetModuleId() const {
    return ModuleId::kModuleIdPath;
}

std::string PathInterfaceImpl::RequestPath(const PathQueryPtr& query) {
    // Generate a UUID for this request
    std::string uuid = Uuid::GetUuid();

    query_ = query;

    // 将请求添加到待处理列表
    {
        std::lock_guard<std::mutex> lock(requests_mutex_);
        pending_requests_[uuid] = false;  // false 表示尚未开始执行
    }

    if (query != nullptr && !query->path_points.empty()) {
        // if (first landmark's waypoint_type not equal to kStartPoint), fill by EgoMatch
        if (query->path_points[0]->waypoint_type != WayPointType::kStartPoint) {
            PathLandmarkPtr ego_mark_ptr = std::make_shared<PathLandmark>();
            ego_mark_ptr->valid = true;
            ego_mark_ptr->waypoint_type = WayPointType::kStartPoint;
            ego_mark_ptr->landmark_type = LandmarkType::kEgo;
            uint64_t cur_time = Time::Now().ToMillisecond();
            loc::MatchResult match_result = map_matching_manager_->GetLatestMatchResult();
            if (abs_diff(match_result.timestamp, cur_time) < 5000) {  // 5s
                ego_mark_ptr->pt = match_result.origin_pos.lnglat;
                // insert at begin
                query->path_points.insert(query->path_points.begin(), ego_mark_ptr);
                LOG_INFO("insert Ego position: {}, {}", ego_mark_ptr->pt.x(), ego_mark_ptr->pt.y());
            }
            // else {
            //     LOG_ERROR("match result is too old, timestamp: {}, cur_time: {}", match_result.timestamp, cur_time);
            // }
        }
    }

    // todo: get ego_pt from matching module
    if (query == nullptr || query->path_points.size() < 2) {
        LOG_ERROR("Path query must have at least 2 path_points");
        // constrcut PathResult and send to listener
        auto result = std::make_shared<PathResult>();
        result->uuid = uuid;
        result->status = "error";
        result->code = static_cast<int>(ErrorCode::kErrorCodeFailed);
        for (const auto& listener : listeners_) {
            listener->OnPathResult(query, result);
        }
        return uuid;
    }

    // Create a copy of the query for the thread
    PathQuery query_new = *query;
    // deep copy query
    for (auto& path_point : query_new.path_points) {
        path_point = std::make_shared<PathLandmark>(*path_point);
        DoLandMarkMatching(path_point);
    }

    // add log
    LOG_INFO("start path calculation, uuid: {}, strategy: {}, timestamp: {}", uuid, GetStrategyString(query_new.strategy), Time::Now().ToMillisecond());

    // Post the path calculation task to the thread
    path_thread_->Post([this, query_new, uuid]() {
        // 检查请求是否已被取消
        {
            std::lock_guard<std::mutex> lock(requests_mutex_);
            auto it = pending_requests_.find(uuid);
            if (it == pending_requests_.end()) {
                LOG_INFO("Request {} was cancelled before execution", uuid);
                return;  // 请求已被取消，直接返回
            }
            // 标记为正在执行
            it->second = true;
            current_executing_uuid_ = uuid;
        }

        // 根据PathMode选择算路方式
        PathResultPtr result = nullptr;
        auto query_ptr = std::make_shared<PathQuery>(query_new);

        if (query_new.mode == PathMode::kOnline) {
            // 在线算路
            if (cloud_route_client_) {
                LOG_INFO("Using online routing for UUID: {}", uuid);
                result = cloud_route_client_->RequestRouteSync(query_new);
                if (result) {
                    result->uuid = uuid;
                } else {
                    // 在线算路失败，创建错误结果
                    result = std::make_shared<PathResult>();
                    result->uuid = uuid;
                    result->status = "error";
                    result->code = static_cast<int>(ErrorCode::kErrorCodePathOnlineError);
                    LOG_ERROR("Online routing failed for UUID: {}", uuid);
                }
            } else {
                // 云端客户端未初始化，返回错误
                result = std::make_shared<PathResult>();
                result->uuid = uuid;
                result->status = "error";
                result->code = static_cast<int>(ErrorCode::kErrorCodePathOnlineError);
                LOG_ERROR("Cloud route client not available for UUID: {}", uuid);
            }
        } else if (query_new.mode == PathMode::kOffline) {
            // 离线算路
            LOG_INFO("Using offline routing for UUID: {}", uuid);
            path_engine_->SetInterruptCallback(nullptr);
            path_engine_->Clear();
            graph_reader_ptr_->Init();

            result = path_engine_->CalculateRoute(query_new);
            result->uuid = uuid;

            const auto& metrics = path_engine_->GetMetrics();
            debug_manager_->WritePerformanceMetrics(metrics, uuid);

#ifdef PATH_DEBUG
            graph_reader_ptr_->PrintStatistics();
#endif
            graph_reader_ptr_->Clear();
        } else {
            // kAuto模式：优先尝试离线，失败时尝试在线
            LOG_INFO("Using auto routing mode for UUID: {}", uuid);

            // 先尝试离线算路
            path_engine_->SetInterruptCallback(nullptr);
            path_engine_->Clear();
            graph_reader_ptr_->Init();

            result = path_engine_->CalculateRoute(query_new);
            result->uuid = uuid;

            // 如果离线算路失败且云端客户端可用，尝试在线算路
            if (result->code != static_cast<int>(ErrorCode::kErrorCodeOk) && cloud_route_client_) {
                LOG_INFO("Offline routing failed, trying online routing for UUID: {}", uuid);
                PathResultPtr online_result = cloud_route_client_->RequestRouteSync(query_new);
                if (online_result && online_result->code == static_cast<int>(ErrorCode::kErrorCodeOk)) {
                    online_result->uuid = uuid;
                    result = online_result;
                    LOG_INFO("Online routing succeeded for UUID: {}", uuid);
                }
            }

            const auto& metrics = path_engine_->GetMetrics();
            debug_manager_->WritePerformanceMetrics(metrics, uuid);

#ifdef PATH_DEBUG
            graph_reader_ptr_->PrintStatistics();
#endif
            graph_reader_ptr_->Clear();
        }

        // 写入调试信息
        debug_manager_->WritePathQuery(query_ptr, uuid);
        debug_manager_->WritePathResult(*result, uuid);

        LOG_INFO("send path result, uuid: {}, status: {}, tag: {}, code: {:x}, paths size: {}, mode: {}",
            result->uuid, result->status, result->tag, result->code, result->paths.size(),
            static_cast<int>(query_new.mode));

        // Call listeners with the result
        for (const auto& listener : listeners_) {
            listener->OnPathResult(query_ptr, result);
        }

        // 清理请求状态
        {
            std::lock_guard<std::mutex> lock(requests_mutex_);
            pending_requests_.erase(uuid);
            current_executing_uuid_ = "";  // 清除当前执行的UUID
        }
    });

    // Return the UUID immediately so the caller can track the request
    return uuid;
}

int32_t PathInterfaceImpl::CancelRequest(const std::string& uuid) {
    std::lock_guard<std::mutex> lock(requests_mutex_);

    auto it = pending_requests_.find(uuid);
    if (it == pending_requests_.end()) {
        LOG_WARN("Request {} not found, may have already completed or been cancelled", uuid);
        return static_cast<int32_t>(ErrorCode::kErrorCodeFailed);
    }

    bool is_executing = it->second;

    if (!is_executing) {
        // 请求尚未开始执行，直接从队列中移除
        pending_requests_.erase(it);
        LOG_INFO("Request {} cancelled before execution", uuid);
        return static_cast<int32_t>(ErrorCode::kErrorCodeOk);
    } else {
        // 请求正在执行，需要中断算路
        if (current_executing_uuid_ == uuid) {
            LOG_INFO("Cancelling currently executing request {}", uuid);
            path_engine_->CancelCurrentCalculation();
            return static_cast<int32_t>(ErrorCode::kErrorCodeOk);
        } else {
            LOG_WARN("Request {} is marked as executing but not currently running", uuid);
            pending_requests_.erase(it);
            return static_cast<int32_t>(ErrorCode::kErrorCodeOk);
        }
    }
}

bool PathInterfaceImpl::AddPathListener(const PathListenerPtr& listener) {
    if (listener == nullptr) {
        return false;
    }
    listeners_.push_back(listener);
    return true;
}

bool PathInterfaceImpl::RemovePathListener(const PathListenerPtr& listener) {
    if (listener == nullptr) {
        return false;
    }
    auto it = std::find(listeners_.begin(), listeners_.end(), listener);
    if (it != listeners_.end()) {
        listeners_.erase(it);
        return true;
    }
    return false;
}

int32_t PathInterfaceImpl::DoLandMarkMatching(PathLandmarkPtr& landmark) {
    // TODO: Implement landmark matching logic
    if (!landmark->valid) {
        LOG_ERROR("landmark is not valid");
        return -1;
    }

    if (!landmark->candidates.empty()) {
        LOG_ERROR("landmark has candidates");
        return -1;
    }

    if (landmark->waypoint_type == WayPointType::kStartPoint) {
        uint64_t cur_time = Time::Now().ToMillisecond();
        loc::MatchResult match_result = map_matching_manager_->GetLatestMatchResult();
        if (match_result.timestamp == 0U) {
            return -1;
        }
        if (abs_diff(match_result.timestamp, cur_time) > 5000) { // 5s
            LOG_ERROR("match result is too old, timestamp: {}, cur_time: {}", match_result.timestamp, cur_time);
            return -1;
        }
        GraphId road_id{match_result.road_match_info.tile_id, static_cast<uint32_t>(match_result.road_match_info.link_id), match_result.road_match_info.dir == 0 ? true : false };

        if (!graph_reader_ptr_->EgoMatch(match_result.origin_pos.lnglat, road_id, match_result.road_match_info.dir, match_result.road_match_info.offset, landmark)) {
            LOG_ERROR("EgoMatch failed");
            return -1;
        }
        LOG_INFO("EgoMatch success");
    } else {
        // todo
        // loc_->StaticMatch(landmark);
    }

    // TODO: Implement landmark matching logic
    return 0;
}

void PathInterfaceImpl::OnMapMatchingCallback(const loc::MatchResult& result) {
    // 委托给 MapMatchingManager 处理
    map_matching_manager_->ProcessMatchResult(result);

    // 检查是否需要重新路由
    if (map_matching_manager_->ShouldReroute()) {
        if (query_) {
            std::string uuid = ReRoute(query_);
            LOG_INFO("ReRoute uuid: {}", uuid);
        }
    }
}

std::string PathInterfaceImpl::ReRoute(const PathQueryPtr& query) {
    // if has kStart point, delete it, and clal RequestPath()
    if (query != nullptr && !query->path_points.empty()) {
        // if (first landmark's waypoint_type not equal to kStartPoint), fill by EgoMatch
        if (query->path_points[0]->waypoint_type != WayPointType::kStartPoint) {
            // erase first landmark
            query->path_points.erase(query->path_points.begin());
        }
    }
    return RequestPath(query);
}

}  // namespace path
}  // namespace aurora
