#ifndef AURORA_PATH_SRC_CONFIG_PATH_CONFIG_H_
#define AURORA_PATH_SRC_CONFIG_PATH_CONFIG_H_

#include <string>
#include <memory>
#include <unordered_map>
#include <vector>
#include <variant>
#include <yaml-cpp/yaml.h>
#include "base/include/pointll.h"
#include "base/include/module.h"

namespace aurora {
namespace path {

/**
 * @brief Class to handle path configuration
 * 
 * This class loads and provides access to the path configuration from a YAML file.
 * It caches configuration values to avoid issues with YAML::Node reference sharing.
 */
class PathConfig {
public:
    // Define the types that can be stored in the configuration cache
    using ConfigValue = std::variant<
        std::string,
        int,
        double,
        bool,
        std::vector<double>,
        std::vector<int>,
        std::vector<std::string>
    >;

    /**
     * @brief Constructor
     */
    PathConfig() = default;

    /**
     * @brief Destructor
     */
    ~PathConfig() = default;

    /**
     * @brief Load configuration from file
     * 
     * @param config_file Path to the configuration file
     * @return true if loading was successful, false otherwise
     */
    bool Load(const std::string& config_file);

    void SetParams(const ParameterMap &parameters);

    /**
     * @brief Get a configuration value
     * 
     * @param key Configuration key (supports nested keys with '.' separator)
     * @return ConfigValue representing the configuration value
     */
    template<typename T>
    T Get(const std::string& key, const T& default_value = T()) const {
        auto it = config_cache_.find(key);
        if (it != config_cache_.end()) {
            try {
                // Direct match - try to get the exact type
                return std::get<T>(it->second);
            } catch (const std::bad_variant_access&) {
                // return default value
                return default_value;
            }
        }
        return default_value;
    }

    /**
     * @brief Check if a configuration key exists
     * 
     * @param key Configuration key (supports nested keys with '.' separator)
     * @return true if the key exists, false otherwise
     */
    bool HasKey(const std::string& key) const;

    /**
     * @brief Get the data directory path
     * 
     * @return Data directory path
     */
    std::string GetDataDir() const;

    /**
     * @brief Get the center point
     * 
     * @return Center point as PointLL
     */
    PointLL GetCenterPoint() const;

    /**
     * @brief Get the match radius
     * 
     * @return Match radius
     */
    double GetMatchRadius() const;

    /**
     * @brief Get the debug output path
     * 
     * @return Debug output path
     */
    std::string GetDebugOutPath() const;

    /**
     * @brief Check if debug mode is enabled
     * 
     * @return true if debug mode is enabled, false otherwise
     */
    bool IsDebugEnabled() const;

    /**
     * @brief Get the route algorithm
     *
     * @return Route algorithm name
     */
    std::string GetRouteAlgorithm() const;

    /**
     * @brief Get the cloud route server URL
     *
     * @return Cloud route server URL
     */
    std::string GetCloudRouteServerUrl() const;

    /**
     * @brief Get the cloud route timeout
     *
     * @return Cloud route timeout in seconds
     */
    int GetCloudRouteTimeout() const;

private:
    // Helper method to recursively extract and cache all values from YAML
    void CacheConfigValues(const YAML::Node& node, const std::string& prefix = "");
    
    // Helper method to extract a value from YAML and convert to appropriate type
    ConfigValue ExtractValue(const YAML::Node& node) const;
    
    // Original YAML configuration root node (kept for reference)
    YAML::Node config_root_;
    
    // Cached configuration values
    std::unordered_map<std::string, ConfigValue> config_cache_;
    
    // Cached common values for quick access
    std::string data_dir_;
    PointLL center_point_;
    double match_radius_;
    std::string debug_out_path_;
    bool debug_enabled_;
    std::string route_algorithm_;
    std::string cloud_route_server_url_;
    int cloud_route_timeout_;
};

// Singleton instance of PathConfig
class PathConfigManager {
public:
    /**
     * @brief Get the PathConfig instance
     * 
     * @return Reference to the PathConfig instance
     */
    static PathConfig& Instance();

    /**
     * @brief Initialize the PathConfig with a configuration file
     * 
     * @param config_file Path to the configuration file
     * @return true if initialization was successful, false otherwise
     */
    static bool Initialize(const std::string& config_file);

private:
    // Singleton instance
    static std::unique_ptr<PathConfig> instance_;
};

}  // namespace path
}  // namespace aurora

#endif  // AURORA_PATH_SRC_CONFIG_PATH_CONFIG_H_
