#include "path/src/config/path_config.h"
#include <fstream>
#include <sstream>
#include "base/include/logger.h"

namespace aurora {
namespace path {

// Initialize the static instance
std::unique_ptr<PathConfig> PathConfigManager::instance_ = nullptr;

bool PathConfig::Load(const std::string& config_file) {
    try {
        // Load the YAML file
        config_root_ = YAML::LoadFile(config_file);

        // Cache all configuration values
        CacheConfigValues(config_root_);

        // Cache common values for quick access
        data_dir_ = Get<std::string>("data_dir", ".");

        // Cache center point
        std::vector<double> center_pt = Get<std::vector<double>>("center_pt", {0.0, 0.0});
        if (center_pt.size() >= 2) {
            center_point_ = PointLL(center_pt[0], center_pt[1]);
        } else {
            center_point_ = PointLL(0.0, 0.0);
        }

        match_radius_ = Get<double>("match_radius", 1.0f);
        debug_out_path_ = Get<std::string>("debug_out_path", ".");
        debug_enabled_ = Get<bool>("debug_flag", false);
        route_algorithm_ = Get<std::string>("route_algorithm", "dijkstra");
        cloud_route_server_url_ = Get<std::string>("cloud_route.server_url", "http://localhost:8080");
        cloud_route_timeout_ = Get<int>("cloud_route.timeout", 30);

        return true;
    } catch (const YAML::Exception& e) {
        LOG_ERROR("Failed to load configuration file: {}, error: {}", config_file, e.what());
        return false;
    }
}

void PathConfig::SetParams(const ParameterMap &parameters) {
    // merge params in config_cache
    try {
        if (parameters.count("data_dir")) {
            data_dir_ = std::get<std::string>(parameters.at("data_dir"));
            LOG_INFO("Update data dir:{}", data_dir_);
        }
    } catch (const std::bad_variant_access&) {
        // return default value
        LOG_ERROR("dir_path ser error");
    }
}

void PathConfig::CacheConfigValues(const YAML::Node& node, const std::string& prefix) {
    // Skip invalid nodes
    if (!node.IsDefined() || node.IsNull()) {
        return;
    }

    // Handle different node types
    if (node.IsScalar() || node.IsSequence()) {
        // Store scalar or sequence value with its key
        if (!prefix.empty()) {
            config_cache_[prefix] = ExtractValue(node);
        }
    } else if (node.IsMap()) {
        // For maps, recursively process each child
        for (const auto& it : node) {
            std::string key = it.first.as<std::string>();
            std::string new_prefix = prefix.empty() ? key : prefix + "." + key;

            // Store this map entry
            config_cache_[new_prefix] = ExtractValue(it.second);

            // Recursively process children
            CacheConfigValues(it.second, new_prefix);
        }
    }
}

PathConfig::ConfigValue PathConfig::ExtractValue(const YAML::Node& node) const {
    if (!node.IsDefined() || node.IsNull()) {
        return std::string("");
    }
    
    if (node.IsScalar()) {
        std::string str_value = node.as<std::string>();

        // Check for boolean values
        if (str_value == "true" || str_value == "false" || 
            str_value == "yes" || str_value == "no" || 
            str_value == "on" || str_value == "off") {
            return node.as<bool>();
        }

        // Check for numeric values with more reliable method
        // First check if it's a valid number by attempting to parse it
        try {
            // Check if it has a decimal point (likely a double)
            if (str_value.find('.') != std::string::npos) {
                return node.as<double>();
            }

            // No decimal point, try as integer first
            int int_value = std::stoi(str_value);
            // If the string representation of the integer matches the original string,
            // it's a pure integer without decimal part
            if (std::to_string(int_value) == str_value) {
                return int_value;
            } else {
                // It might be a double without decimal part or something else
                return node.as<double>();
            }
        } catch (...) {
            // Not a number, return as string
            return str_value;
        }
    } else if (node.IsSequence()) {
        // Try to determine the type of sequence elements
        if (node.size() > 0) {
            // Check first element to guess the sequence type
            auto first = node[0];
            if (first.IsScalar()) {
                try {
                    // Try as double sequence
                    std::vector<double> double_vec;
                    for (const auto& item : node) {
                        double_vec.push_back(item.as<double>());
                    }
                    return double_vec;
                } catch (...) {
                    try {
                        // Try as int sequence
                        std::vector<int> int_vec;
                        for (const auto& item : node) {
                            int_vec.push_back(item.as<int>());
                        }
                        return int_vec;
                    } catch (...) {
                        // Default to string sequence
                        std::vector<std::string> str_vec;
                        for (const auto& item : node) {
                            str_vec.push_back(item.as<std::string>());
                        }
                        return str_vec;
                    }
                }
            }
        }
        // Empty sequence or non-scalar elements, return empty string vector
        return std::vector<std::string>();
    }

    // Default for maps or other types
    return std::string("");
}

bool PathConfig::HasKey(const std::string& key) const {
    return config_cache_.find(key) != config_cache_.end();
}

std::string PathConfig::GetDataDir() const {
    return data_dir_;
}

PointLL PathConfig::GetCenterPoint() const {
    return center_point_;
}

double PathConfig::GetMatchRadius() const {
    return match_radius_;
}

std::string PathConfig::GetDebugOutPath() const {
    return debug_out_path_;
}

bool PathConfig::IsDebugEnabled() const {
    return debug_enabled_;
}

std::string PathConfig::GetRouteAlgorithm() const {
    return route_algorithm_;
}

std::string PathConfig::GetCloudRouteServerUrl() const {
    return cloud_route_server_url_;
}

int PathConfig::GetCloudRouteTimeout() const {
    return cloud_route_timeout_;
}

PathConfig& PathConfigManager::Instance() {
    if (!instance_) {
        instance_ = std::make_unique<PathConfig>();
    }
    return *instance_;
}

bool PathConfigManager::Initialize(const std::string& config_file) {
    if (!instance_) {
        instance_ = std::make_unique<PathConfig>();
    }
    return instance_->Load(config_file);
}

}  // namespace path
}  // namespace aurora
